<template>
  <div class="profile-page-modern">
    <!-- 现代化页面头部 -->
    <div class="page-header-modern">
      <div class="header-content">
        <div class="header-text">
          <h1 class="page-title">
            <el-icon class="title-icon"><User /></el-icon>
            个人资料
          </h1>
          <p class="page-subtitle">管理您的个人信息和账户设置</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" :icon="Download" @click="exportUserData">
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <div class="profile-container-modern">
      <!-- 现代化侧边栏用户卡片 -->
      <div class="profile-sidebar-modern">
        <div class="user-profile-card">
          <!-- 背景装饰 -->
          <div class="card-background">
          </div>

          <!-- 横排布局容器 -->
          <div class="profile-sections-horizontal">
            <!-- 用户头像区域 -->
            <div class="avatar-section-modern">
              <!-- 加载状态骨架屏 -->
              <div v-if="loading" class="user-skeleton">
                <el-skeleton animated>
                  <template #template>
                    <div class="skeleton-avatar-container">
                      <el-skeleton-item variant="circle" style="width: 80px; height: 80px; margin: 0 auto 12px;" />
                      <el-skeleton-item variant="text" style="width: 100px; height: 20px; margin: 0 auto 6px;" />
                      <el-skeleton-item variant="text" style="width: 80px; height: 16px; margin: 0 auto 8px;" />
                      <el-skeleton-item variant="text" style="width: 120px; height: 14px; margin: 0 auto;" />
                    </div>
                  </template>
                </el-skeleton>
              </div>

              <!-- 实际内容 -->
              <div v-else class="user-content-loaded fade-in">
                <div class="avatar-container">
                  <div class="avatar-uploader-modern" @click="openAvatarDialog">
                    <div class="avatar-wrapper">
                      <img v-if="userInfo.avatar" :src="userInfo.avatar" class="avatar-modern" />
                      <div v-else class="avatar-placeholder">
                        <el-icon class="avatar-icon"><User /></el-icon>
                      </div>
                      <div class="avatar-overlay">
                        <el-icon class="upload-icon"><Camera /></el-icon>
                        <span class="upload-text">更换头像</span>
                      </div>
                    </div>
                  </div>

                  <!-- 在线状态指示器 -->
                  <div class="online-indicator"></div>
                </div>

                <!-- 用户基本信息 -->
                <div class="user-info-modern">
                  <h3 class="username">{{ userInfo.username }}</h3>
                  <div class="user-meta">
                    <el-tag
                      :type="getRoleTagType(userInfo.role)"
                      class="role-tag-modern"
                      effect="light"
                    >
                      <el-icon class="role-icon"><Crown /></el-icon>
                      {{ getRoleText(userInfo.role) }}
                    </el-tag>
                  </div>
                  <p class="last-login-modern">
                    <el-icon class="time-icon"><Clock /></el-icon>
                    上次登录：{{ formatTime(userInfo.last_login) }}
                  </p>
                </div>
              </div>
            </div>

            <!-- 个人资料完整度 -->
            <div class="profile-completeness">
              <div v-if="loading" class="completeness-skeleton">
                <el-skeleton animated>
                  <template #template>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                      <el-skeleton-item variant="text" style="width: 80px; height: 16px;" />
                      <el-skeleton-item variant="text" style="width: 40px; height: 16px;" />
                    </div>
                    <el-skeleton-item variant="rect" style="width: 100%; height: 8px; border-radius: 4px;" />
                  </template>
                </el-skeleton>
              </div>
              <div v-else class="completeness-content">
                <div class="completeness-header">
                  <span class="completeness-title">资料完整度</span>
                  <span class="completeness-percentage">{{ profileCompleteness }}%</span>
                </div>
                <el-progress
                  :percentage="profileCompleteness"
                  :color="getCompletenessColor(profileCompleteness)"
                  :stroke-width="8"
                  class="completeness-progress"
                />
                <div v-if="profileCompleteness < 100" class="completeness-suggestions">
                  <div class="suggestions-header">
                    <el-icon class="suggestion-icon"><InfoFilled /></el-icon>
                    <span class="suggestion-title">完善建议</span>
                  </div>
                  <ul class="suggestions-list">
                    <li v-for="suggestion in profileSuggestions" :key="suggestion.field" class="suggestion-item">
                      <el-icon class="item-icon"><component :is="suggestion.icon" /></el-icon>
                      <span class="item-text">{{ suggestion.text }}</span>
                      <el-button
                        type="text"
                        size="small"
                        @click="focusField(suggestion.field)"
                        class="item-action"
                      >
                        完善
                      </el-button>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 统计信息卡片 -->
            <div class="stats-section-modern">
              <div v-if="loading" class="stats-skeleton">
                <el-skeleton animated>
                  <template #template>
                    <div v-for="i in 3" :key="i" class="stat-skeleton-item">
                      <el-skeleton-item variant="circle" style="width: 36px; height: 36px;" />
                      <div style="flex: 1; margin-left: 12px;">
                        <el-skeleton-item variant="text" style="width: 60px; height: 12px; margin-bottom: 4px;" />
                        <el-skeleton-item variant="text" style="width: 80px; height: 14px;" />
                      </div>
                    </div>
                  </template>
                </el-skeleton>
              </div>
              <div v-else class="stats-content">
                <div class="stat-card" v-for="stat in userStats" :key="stat.key">
                  <div class="stat-icon" :class="stat.iconClass">
                    <el-icon><component :is="stat.icon" /></el-icon>
                  </div>
                  <div class="stat-content">
                    <span class="stat-label">{{ stat.label }}</span>
                    <span class="stat-value">{{ stat.value }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 安全评分 -->
            <div class="security-score-card">
              <div v-if="loading" class="security-skeleton">
                <el-skeleton animated>
                  <template #template>
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
                      <el-skeleton-item variant="circle" style="width: 18px; height: 18px;" />
                      <el-skeleton-item variant="text" style="width: 60px; height: 14px;" />
                    </div>
                    <div style="display: flex; align-items: center; gap: 16px;">
                      <el-skeleton-item variant="circle" style="width: 60px; height: 60px;" />
                      <div style="flex: 1;">
                        <el-skeleton-item variant="text" style="width: 40px; height: 16px; margin-bottom: 4px;" />
                        <el-skeleton-item variant="text" style="width: 60px; height: 12px;" />
                      </div>
                    </div>
                  </template>
                </el-skeleton>
              </div>
              <div v-else class="security-content">
                <div class="score-header">
                  <el-icon class="security-icon"><Shield /></el-icon>
                  <span class="score-title">安全评分</span>
                </div>
                <div class="score-display">
                  <div class="score-circle">
                    <el-progress
                      type="circle"
                      :percentage="securityScore"
                      :width="60"
                      :color="getSecurityScoreColor(securityScore)"
                    />
                  </div>
                  <div class="score-info">
                    <span class="score-text">{{ getSecurityScoreText(securityScore) }}</span>
                    <el-button
                      type="text"
                      size="small"
                      @click="showSecurityDetails"
                      class="score-detail-btn"
                    >
                      查看详情
                    </el-button>
                  </div>
                </div>
                
                <!-- 安全概览 -->
                <div class="security-summary-content">
                  <div class="summary-header">
                    <el-icon class="summary-icon"><Lock /></el-icon>
                    <span class="summary-title">安全概览</span>
                  </div>
                  <div class="security-items">
                    <div class="security-item">
                      <el-icon class="item-icon success"><Check /></el-icon>
                      <span class="item-text">密码强度良好</span>
                    </div>
                    <div class="security-item">
                      <el-icon class="item-icon warning"><Warning /></el-icon>
                      <span class="item-text">未启用双因子认证</span>
                    </div>
                    <div class="security-item">
                      <el-icon class="item-icon success"><Check /></el-icon>
                      <span class="item-text">登录设备安全</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>

      <!-- 现代化主要内容区域 -->
      <div class="profile-content-modern">
        <el-tabs v-model="activeTab" class="profile-tabs-modern" @tab-change="handleTabChange">
          <!-- 基本信息标签页 -->
          <el-tab-pane name="basic">
            <template #label>
              <div class="tab-label">
                <el-icon><User /></el-icon>
                <span>基本信息</span>
              </div>
            </template>

            <div class="tab-content-wrapper">
              <div class="content-header">
                <h3 class="content-title">基本信息</h3>
                <p class="content-subtitle">管理您的个人基本信息</p>
              </div>

              <el-card class="form-card-modern" shadow="never">
                <el-form
                  ref="basicFormRef"
                  :model="basicForm"
                  :rules="basicRules"
                  label-width="120px"
                  class="profile-form-modern"
                >
                <!-- 用户名字段 -->
                <div class="form-section">
                  <el-form-item label="用户名" prop="username">
                    <div class="input-with-icon">
                      <el-icon class="input-icon"><User /></el-icon>
                      <el-input
                        v-model="basicForm.username"
                        placeholder="请输入用户名"
                        :disabled="!canEditUsername"
                        class="modern-input"
                      />
                    </div>
                    <div class="form-tip-modern">
                      <el-icon class="tip-icon"><InfoFilled /></el-icon>
                      用户名用于登录，一旦设置后不建议修改
                    </div>
                  </el-form-item>
                </div>

                <!-- 真实姓名字段 -->
                <div class="form-section" data-field="full_name">
                  <el-form-item label="真实姓名" prop="full_name">
                    <div class="input-with-icon">
                      <el-icon class="input-icon"><Avatar /></el-icon>
                      <el-input
                        v-model="basicForm.full_name"
                        placeholder="请输入真实姓名"
                        class="modern-input"
                      />
                    </div>
                  </el-form-item>
                </div>

                <!-- 邮箱地址字段 -->
                <div class="form-section" data-field="email">
                  <el-form-item label="邮箱地址" prop="email">
                    <div class="input-with-icon">
                      <el-icon class="input-icon"><Message /></el-icon>
                      <el-input
                        v-model="basicForm.email"
                        type="email"
                        placeholder="请输入邮箱地址"
                        class="modern-input"
                      />
                    </div>
                    <div class="form-tip-modern">
                      <el-icon class="tip-icon"><InfoFilled /></el-icon>
                      邮箱用于接收系统通知和密码重置
                    </div>
                  </el-form-item>
                </div>

                <!-- 手机号码字段 -->
                <div class="form-section" data-field="phone">
                  <el-form-item label="手机号码" prop="phone">
                    <div class="input-with-icon">
                      <el-icon class="input-icon"><Phone /></el-icon>
                      <el-input
                        v-model="basicForm.phone"
                        placeholder="请输入手机号码"
                        class="modern-input"
                      />
                    </div>
                  </el-form-item>
                </div>

                <!-- 部门字段 -->
                <div class="form-section" data-field="department">
                  <el-form-item label="部门" prop="department">
                    <div class="input-with-icon">
                      <el-icon class="input-icon"><OfficeBuilding /></el-icon>
                      <el-input
                        v-model="basicForm.department"
                        placeholder="请输入所属部门"
                        class="modern-input"
                      />
                    </div>
                  </el-form-item>
                </div>

                <!-- 职位字段 -->
                <div class="form-section" data-field="position">
                  <el-form-item label="职位" prop="position">
                    <div class="input-with-icon">
                      <el-icon class="input-icon"><Briefcase /></el-icon>
                      <el-input
                        v-model="basicForm.position"
                        placeholder="请输入职位"
                        class="modern-input"
                      />
                    </div>
                  </el-form-item>
                </div>

                <!-- 个人简介字段 -->
                <div class="form-section" data-field="bio">
                  <el-form-item label="个人简介" prop="bio">
                    <div class="textarea-with-icon">
                      <el-icon class="textarea-icon"><Document /></el-icon>
                      <el-input
                        v-model="basicForm.bio"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入个人简介"
                        maxlength="500"
                        show-word-limit
                        class="modern-textarea"
                      />
                    </div>
                  </el-form-item>
                </div>

                <!-- 表单操作按钮 -->
                <div class="form-actions">
                  <el-button
                    type="primary"
                    @click="saveBasicInfo"
                    :loading="saving"
                    class="action-btn primary-btn"
                  >
                    <el-icon><Check /></el-icon>
                    保存修改
                  </el-button>
                  <el-button
                    @click="resetBasicForm"
                    class="action-btn secondary-btn"
                  >
                    <el-icon><RefreshLeft /></el-icon>
                    重置
                  </el-button>
                </div>
              </el-form>
            </el-card>
            </div>
          </el-tab-pane>
          
          <!-- 安全设置标签页 -->
          <el-tab-pane name="security">
            <template #label>
              <div class="tab-label">
                <el-icon><Lock /></el-icon>
                <span>安全设置</span>
              </div>
            </template>

            <div class="tab-content-wrapper">
              <div class="content-header">
                <h3 class="content-title">安全设置</h3>
                <p class="content-subtitle">管理您的账户安全和认证设置</p>
              </div>

              <!-- 密码修改卡片 -->
              <el-card class="form-card-modern" shadow="never">
                <template #header>
                  <div class="card-header">
                    <div class="header-left">
                      <el-icon class="header-icon"><Key /></el-icon>
                      <div class="header-text">
                        <h4 class="header-title">修改密码</h4>
                        <p class="header-subtitle">定期更换密码可提高账户安全性</p>
                      </div>
                    </div>
                    <div class="header-right">
                      <el-tag type="info" size="small">
                        <el-icon><InfoFilled /></el-icon>
                        建议每90天更换一次
                      </el-tag>
                    </div>
                  </div>
                </template>

                <el-form
                  ref="passwordFormRef"
                  :model="passwordForm"
                  :rules="passwordRules"
                  label-width="120px"
                  class="password-form-modern"
                >
                  <!-- 当前密码字段 -->
                  <div class="form-section">
                    <el-form-item label="当前密码" prop="current_password">
                      <div class="input-with-icon">
                        <el-icon class="input-icon"><Lock /></el-icon>
                        <el-input
                          v-model="passwordForm.current_password"
                          type="password"
                          placeholder="请输入当前密码"
                          show-password
                          class="modern-input"
                        />
                      </div>
                    </el-form-item>
                  </div>

                  <!-- 新密码字段 -->
                  <div class="form-section">
                    <el-form-item label="新密码" prop="new_password">
                      <div class="input-with-icon">
                        <el-icon class="input-icon"><Key /></el-icon>
                        <el-input
                          v-model="passwordForm.new_password"
                          type="password"
                          placeholder="请输入新密码"
                          show-password
                          class="modern-input"
                          @input="(value) => checkPasswordStrength(value)"
                        />
                      </div>

                      <!-- 密码强度指示器 -->
                      <div v-if="passwordStrength" class="password-strength-modern">
                        <div class="strength-header">
                          <span class="strength-label">密码强度</span>
                          <span class="strength-score">{{ passwordStrength.text }}</span>
                        </div>
                        <div class="strength-bar-modern">
                          <div
                            class="strength-fill-modern"
                            :class="passwordStrength.level"
                            :style="{ width: passwordStrength.score + '%' }"
                          ></div>
                        </div>
                        <div class="strength-tips" v-if="passwordStrength.suggestions.length > 0">
                          <el-icon class="tips-icon"><InfoFilled /></el-icon>
                          <ul class="tips-list">
                            <li v-for="tip in passwordStrength.suggestions" :key="tip">{{ tip }}</li>
                          </ul>
                        </div>
                      </div>
                    </el-form-item>
                  </div>

                  <!-- 确认密码字段 -->
                  <div class="form-section">
                    <el-form-item label="确认密码" prop="confirm_password">
                      <div class="input-with-icon">
                        <el-icon class="input-icon"><Lock /></el-icon>
                        <el-input
                          v-model="passwordForm.confirm_password"
                          type="password"
                          placeholder="请再次输入新密码"
                          show-password
                          class="modern-input"
                        />
                      </div>
                    </el-form-item>
                  </div>
                
                <el-form-item>
                  <el-button
                    type="primary"
                    @click="changePassword"
                    :loading="changingPassword"
                  >
                    修改密码
                  </el-button>
                  <el-button @click="resetPasswordForm">重置</el-button>
                </el-form-item>
              </el-form>
              </el-card>
            </div>

            <!-- 双因素认证卡片 -->
            <el-card class="form-card-modern security-card-modern" shadow="never">
              <template #header>
                <div class="card-header">
                  <div class="header-left">
                    <el-icon class="header-icon"><Lock /></el-icon>
                    <div class="header-text">
                      <h4 class="header-title">双因素认证</h4>
                      <p class="header-subtitle">为您的账户提供额外的安全保护</p>
                    </div>
                  </div>
                  <div class="header-right">
                    <el-tag 
                      :type="userInfo.two_factor_enabled ? 'success' : 'warning'"
                      effect="light"
                      class="status-tag-modern"
                    >
                      <el-icon class="tag-icon">
                        <component :is="userInfo.two_factor_enabled ? 'CircleCheck' : 'Warning'" />
                      </el-icon>
                      {{ userInfo.two_factor_enabled ? '已启用' : '未启用' }}
                    </el-tag>
                  </div>
                </div>
              </template>
              
              <div class="two-factor-content">
                <div class="auth-info">
                  <div class="info-item">
                    <el-icon class="info-icon"><Shield /></el-icon>
                    <div class="info-text">
                      <div class="info-title">安全级别</div>
                      <div class="info-desc">{{ userInfo.two_factor_enabled ? '高级安全保护' : '基础密码保护' }}</div>
                    </div>
                  </div>
                  <div class="info-item">
                    <el-icon class="info-icon"><Key /></el-icon>
                    <div class="info-text">
                      <div class="info-title">认证方式</div>
                      <div class="info-desc">{{ userInfo.two_factor_enabled ? '密码 + 动态验证码' : '仅密码验证' }}</div>
                    </div>
                  </div>
                </div>
                
                <div class="auth-actions-modern">
                  <el-button
                    v-if="!userInfo.two_factor_enabled"
                    type="primary"
                    size="large"
                    @click="enableTwoFactor"
                    class="action-button-modern"
                  >
                    <el-icon><Lock /></el-icon>
                    启用双因素认证
                  </el-button>
                  <el-button
                    v-else
                    type="danger"
                    size="large"
                    @click="disableTwoFactor"
                    class="action-button-modern"
                  >
                    <el-icon><Unlock /></el-icon>
                    禁用双因素认证
                  </el-button>
                </div>
              </div>
            </el-card>
            

          </el-tab-pane>
          


          <!-- 管理员管理标签页 -->
          <el-tab-pane label="管理员管理" name="admin" v-if="true">
            <div class="admin-management-modern">
              <!-- 页面头部 -->
              <div class="admin-header-modern">
                <div class="header-info">
                  <h3 class="page-title">
                    <el-icon class="title-icon"><UserFilled /></el-icon>
                    管理员管理
                  </h3>
                  <p class="page-subtitle">管理系统管理员账户，控制权限和访问</p>
                </div>
                <div class="header-stats">
                  <div class="stat-card">
                    <div class="stat-value">{{ adminUsers.length }}</div>
                    <div class="stat-label">总管理员</div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-value">{{ adminUsers.filter(u => u.is_active).length }}</div>
                    <div class="stat-label">活跃用户</div>
                  </div>
                </div>
              </div>

              <!-- 操作工具栏 -->
              <div class="admin-toolbar-modern">
                <div class="toolbar-left">
                  <div class="search-container">
                    <el-input
                      v-model="adminSearchKeyword"
                      placeholder="搜索管理员用户名、邮箱或姓名"
                      @keyup.enter="searchAdminUsers"
                      clearable
                      class="search-input"
                    >
                      <template #prefix>
                        <el-icon><Search /></el-icon>
                      </template>
                    </el-input>
                    <el-button type="primary" @click="searchAdminUsers" class="search-btn">
                      <el-icon><Search /></el-icon>
                      搜索
                    </el-button>
                  </div>
                </div>
                <div class="toolbar-right">
                  <el-button type="primary" @click="openAdminDialog('create')" class="add-btn">
                    <el-icon><Plus /></el-icon>
                    添加管理员
                  </el-button>
                </div>
              </div>

              <!-- 管理员列表卡片 -->
              <el-card class="admin-table-card" shadow="never">

                <!-- 管理员列表表格 -->
                <el-table
                  :data="adminUsers"
                  v-loading="adminLoading"
                  stripe
                  class="admin-table-modern"
                  empty-text="暂无管理员数据"
                >
                  <el-table-column prop="username" label="用户名" width="140" show-overflow-tooltip>
                    <template #default="{ row }">
                      <div class="user-cell">
                        <el-avatar :size="28" :src="row.avatar_url" class="user-avatar">
                          <el-icon><User /></el-icon>
                        </el-avatar>
                        <span class="username">{{ row.username }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="full_name" label="姓名" width="100" show-overflow-tooltip />
                  <el-table-column prop="email" label="邮箱" width="160" show-overflow-tooltip />
                  <el-table-column prop="role" label="角色" width="80">
                    <template #default="{ row }">
                      <el-tag :type="getRoleTagType(row.role)" effect="light" class="role-tag" size="small">
                        {{ getRoleText(row.role) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="department" label="部门" width="100" show-overflow-tooltip />
                  <el-table-column prop="is_active" label="状态" width="80">
                    <template #default="{ row }">
                      <el-tag :type="row.is_active ? 'success' : 'danger'" effect="light" class="status-tag" size="small">
                        <el-icon class="tag-icon">
                          <CircleCheck v-if="row.is_active" />
                          <CircleClose v-else />
                        </el-icon>
                        {{ row.is_active ? '激活' : '禁用' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="last_login" label="最后登录" width="140">
                    <template #default="{ row }">
                      <div class="login-time">
                        <el-icon class="time-icon"><Clock /></el-icon>
                        <span>{{ row.last_login ? formatDateTime(row.last_login) : '从未登录' }}</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="180" fixed="right">
                    <template #default="{ row }">
                      <div class="action-buttons">
                        <el-button
                          type="primary"
                          size="small"
                          @click="openAdminDialog('edit', row)"
                          class="action-btn"
                        >
                          <el-icon><Edit /></el-icon>
                        </el-button>
                        <el-button
                          :type="row.is_active ? 'warning' : 'success'"
                          size="small"
                          @click="toggleAdminStatus(row)"
                          :disabled="row.id === authStore.user?.id"
                          class="action-btn"
                        >
                          {{ row.is_active ? '禁用' : '激活' }}
                        </el-button>
                        <el-button
                          type="danger"
                          size="small"
                          @click="deleteAdmin(row)"
                          :disabled="row.id === authStore.user?.id"
                          class="action-btn"
                        >
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 分页区域 -->
                <div class="table-footer">
                  <div class="pagination-info">
                    <span class="info-text">
                      共 {{ adminPagination.total }} 条记录，
                      第 {{ adminPagination.page }} / {{ Math.ceil(adminPagination.total / adminPagination.pageSize) }} 页
                    </span>
                  </div>
                  <el-pagination
                    v-model:current-page="adminPagination.page"
                    v-model:page-size="adminPagination.pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="adminPagination.total"
                    layout="sizes, prev, pager, next, jumper"
                    @size-change="loadAdminUsers"
                    @current-change="loadAdminUsers"
                    class="table-pagination"
                  />
                </div>
              </el-card>
            </div>
          </el-tab-pane>

          <!-- 登录历史标签页 -->
          <el-tab-pane label="登录历史" name="loginHistory">
            <el-card class="form-card-modern security-card-modern" shadow="never">
              <template #header>
                <div class="card-header">
                  <div class="header-left">
                    <el-icon class="header-icon"><Clock /></el-icon>
                    <div class="header-text">
                      <h4 class="header-title">登录历史</h4>
                      <p class="header-subtitle">查看您的账户登录记录和安全状态</p>
                    </div>
                  </div>
                  <div class="header-right">
                    <el-tag type="info" effect="light" class="status-tag-modern">
                      <el-icon class="tag-icon"><Monitor /></el-icon>
                      共 {{ loginHistoryTotal }} 条记录
                    </el-tag>
                  </div>
                </div>
              </template>

              <!-- 登录历史加载骨架屏 -->
              <div v-if="loginHistoryLoading" class="login-history-skeleton-modern">
                <el-skeleton animated>
                  <template #template>
                    <div class="table-skeleton-modern">
                      <div class="table-header-skeleton-modern">
                        <el-skeleton-item variant="text" style="width: 120px; height: 16px;" />
                        <el-skeleton-item variant="text" style="width: 100px; height: 16px;" />
                        <el-skeleton-item variant="text" style="width: 200px; height: 16px;" />
                        <el-skeleton-item variant="text" style="width: 80px; height: 16px;" />
                        <el-skeleton-item variant="text" style="width: 60px; height: 16px;" />
                      </div>
                      <div v-for="i in 5" :key="i" class="table-row-skeleton-modern">
                        <el-skeleton-item variant="text" style="width: 120px; height: 14px;" />
                        <el-skeleton-item variant="text" style="width: 100px; height: 14px;" />
                        <el-skeleton-item variant="text" style="width: 200px; height: 14px;" />
                        <el-skeleton-item variant="text" style="width: 80px; height: 14px;" />
                        <el-skeleton-item variant="rect" style="width: 50px; height: 20px; border-radius: 4px;" />
                      </div>
                    </div>
                  </template>
                </el-skeleton>
              </div>

              <!-- 实际登录历史表格 -->
              <div v-else class="login-history-content-modern">
                <div class="login-history-stats">
                  <div class="stat-item">
                    <el-icon class="stat-icon success"><CircleCheck /></el-icon>
                    <div class="stat-text">
                      <div class="stat-value">{{ loginHistory.filter(item => item.status === 'success').length }}</div>
                      <div class="stat-label">成功登录</div>
                    </div>
                  </div>
                  <div class="stat-item">
                    <el-icon class="stat-icon danger"><CircleClose /></el-icon>
                    <div class="stat-text">
                      <div class="stat-value">{{ loginHistory.filter(item => item.status !== 'success').length }}</div>
                      <div class="stat-label">失败尝试</div>
                    </div>
                  </div>
                  <div class="stat-item">
                    <el-icon class="stat-icon info"><Location /></el-icon>
                    <div class="stat-text">
                      <div class="stat-value">{{ new Set(loginHistory.map(item => item.location || '未知')).size }}</div>
                      <div class="stat-label">登录地点</div>
                    </div>
                  </div>
                </div>
                
                <el-table :data="loginHistory" class="login-history-table-modern">
                  <el-table-column prop="login_time" label="登录时间" width="180">
                    <template #default="{ row }">
                      <div class="time-cell-modern">
                        <el-icon class="cell-icon"><Clock /></el-icon>
                        <div class="cell-content">
                          <div class="cell-main">{{ formatTime(row.login_time) }}</div>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="ip_address" label="IP地址" width="150">
                    <template #default="{ row }">
                      <div class="ip-cell-modern">
                        <el-icon class="cell-icon"><Monitor /></el-icon>
                        <div class="cell-content">
                          <div class="cell-main">{{ row.ip_address }}</div>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="user_agent" label="设备信息">
                    <template #default="{ row }">
                      <div class="device-cell-modern">
                        <el-icon class="cell-icon"><Cellphone /></el-icon>
                        <div class="cell-content">
                          <div class="cell-main">{{ row.user_agent }}</div>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="location" label="登录地点" width="120">
                    <template #default="{ row }">
                      <div class="location-cell-modern">
                        <el-icon class="cell-icon"><Location /></el-icon>
                        <div class="cell-content">
                          <div class="cell-main">{{ row.location || '未知' }}</div>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag
                        :type="row.status === 'success' ? 'success' : 'danger'"
                        class="status-tag-modern"
                        effect="light"
                      >
                        <el-icon class="tag-icon">
                          <component :is="row.status === 'success' ? 'CircleCheck' : 'CircleClose'" />
                        </el-icon>
                        {{ row.status === 'success' ? '成功' : '失败' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>

                <div class="table-pagination-modern">
                  <el-pagination
                    v-model:current-page="loginHistoryPage"
                    v-model:page-size="loginHistoryPageSize"
                    :total="loginHistoryTotal"
                    :page-sizes="[10, 20, 50]"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="loadLoginHistory"
                    @current-change="loadLoginHistory"
                    class="modern-pagination"
                  />
                </div>
              </div>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 管理员编辑对话框 -->
    <el-dialog
      v-model="adminDialogVisible"
      :title="adminDialogMode === 'create' ? '添加管理员' : '编辑管理员'"
      width="600px"
      @close="resetAdminForm"
    >
      <el-form
        ref="adminFormRef"
        :model="adminForm"
        :rules="adminFormRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="adminForm.username" placeholder="请输入用户名" />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="adminForm.email" placeholder="请输入邮箱地址" />
        </el-form-item>

        <el-form-item label="密码" prop="password" v-if="adminDialogMode === 'create'">
          <el-input
            v-model="adminForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="姓名" prop="full_name">
          <el-input v-model="adminForm.full_name" placeholder="请输入真实姓名" />
        </el-form-item>

        <el-form-item label="角色" prop="role">
          <el-select v-model="adminForm.role" placeholder="请选择角色">
            <el-option label="超级管理员" value="superadmin" />
            <el-option label="管理员" value="admin" />
            <el-option label="编辑员" value="editor" />
            <el-option label="查看员" value="viewer" />
          </el-select>
        </el-form-item>

        <el-form-item label="部门">
          <el-input v-model="adminForm.department" placeholder="请输入部门" />
        </el-form-item>

        <el-form-item label="职位">
          <el-input v-model="adminForm.position" placeholder="请输入职位" />
        </el-form-item>

        <el-form-item label="手机号">
          <el-input v-model="adminForm.phone" placeholder="请输入手机号" />
        </el-form-item>

        <el-form-item label="个人简介">
          <el-input
            v-model="adminForm.bio"
            type="textarea"
            :rows="3"
            placeholder="请输入个人简介"
          />
        </el-form-item>

        <el-form-item label="状态">
          <el-switch
            v-model="adminForm.is_active"
            active-text="激活"
            inactive-text="禁用"
          />
        </el-form-item>

        <el-form-item label="超级用户">
          <el-switch
            v-model="adminForm.is_superuser"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="adminDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAdmin" :loading="saving">
            {{ adminDialogMode === 'create' ? '创建' : '更新' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 头像上传对话框 -->
    <el-dialog
      v-model="avatarDialogVisible"
      title="更换头像"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="avatar-upload-dialog">
        <div class="upload-area">
          <el-upload
            class="avatar-upload-input"
            :show-file-list="false"
            :before-upload="handleAvatarSelect"
            accept="image/*"
            drag
          >
            <div v-if="!avatarPreviewUrl" class="upload-placeholder">
              <el-icon class="upload-icon-large"><Plus /></el-icon>
              <div class="upload-text-large">点击或拖拽上传头像</div>
              <div class="upload-hint">支持 JPG、PNG 格式，建议尺寸 200x200</div>
            </div>
            <div v-else class="avatar-preview-container">
              <img :src="avatarPreviewUrl" class="avatar-preview" />
              <div class="preview-overlay">
                <el-icon class="change-icon"><Edit /></el-icon>
                <span>点击更换</span>
              </div>
            </div>
          </el-upload>
        </div>

        <div v-if="avatarPreviewUrl" class="avatar-actions">
          <div v-if="avatarUploading" class="upload-progress">
            <el-progress :percentage="uploadProgress" :stroke-width="6" />
            <p class="progress-text">正在上传头像...</p>
          </div>

          <div v-else class="action-buttons">
            <el-button @click="resetAvatar">重新选择</el-button>
            <el-button type="primary" @click="uploadAvatar" :loading="avatarUploading">
              <el-icon><Upload /></el-icon>
              上传头像
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Lock, Edit, Delete, User, Search, Camera, Clock,
  InfoFilled, Avatar, Message, Phone, Document,
  Check, RefreshLeft, Key, Download, Monitor, Cellphone, Location,
  CircleCheck, CircleClose, Calendar, Upload, Setting,
  Bell, Warning, Star
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/store/auth'
import { profileApi } from '@/api/profile'
import { adminApi } from '@/api/admin'
import type { FormInstance, FormRules, UploadProps } from 'element-plus'
import type { AdminUser, AdminUserCreate, AdminUserUpdate } from '@/api/admin'

const authStore = useAuthStore()
const activeTab = ref('basic')
const saving = ref(false)
const loading = ref(true)
const changingPassword = ref(false)

// 表单引用
const basicFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()
const adminFormRef = ref<FormInstance>()

// 管理员管理相关数据
const adminUsers = ref<AdminUser[]>([])
const adminLoading = ref(false)
const adminDialogVisible = ref(false)
const adminDialogMode = ref<'create' | 'edit'>('create')
const adminSearchKeyword = ref('')
const adminPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 管理员表单数据
const adminForm = reactive<AdminUserCreate>({
  username: '',
  email: '',
  password: '',
  full_name: '',
  role: 'viewer',
  is_active: true,
  is_superuser: false,
  phone: '',
  department: '',
  position: '',
  bio: ''
})

const currentEditingAdmin = ref<AdminUser | null>(null)

// 用户信息
const userInfo = reactive({
  id: 0,
  username: '',
  full_name: '',
  email: '',
  phone: '',
  department: '',
  position: '',
  bio: '',
  avatar: '',
  role: 'user',
  is_active: true,
  two_factor_enabled: false,
  last_login: '',
  created_at: '',
  login_count: 0
})

// 基本信息表单
const basicForm = reactive({
  username: '',
  full_name: '',
  email: '',
  phone: '',
  department: '',
  position: '',
  bio: ''
})

// 密码修改表单
const passwordForm = reactive({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

// 密码强度
const passwordStrength = ref<{
  score: number
  level: string
  text: string
  suggestions: string[]
} | null>(null)

// 头像上传相关
const avatarDialogVisible = ref(false)
const avatarPreviewUrl = ref('')
const avatarFile = ref<File | null>(null)
const avatarUploading = ref(false)
const uploadProgress = ref(0)



// 登录历史
const loginHistory = ref([])
const loginHistoryPage = ref(1)
const loginHistoryPageSize = ref(10)
const loginHistoryTotal = ref(0)
const loginHistoryLoading = ref(false)

// 表单验证规则
const basicRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' }
  ]
}

const passwordRules: FormRules = {
  current_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度至少8位', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.new_password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 管理员表单验证规则
const adminFormRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  full_name: [
    { max: 100, message: '姓名长度不能超过100个字符', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 计算属性
const canEditUsername = computed(() => {
  return authStore.user?.role === 'admin'
})

const uploadUrl = computed(() => {
  return '/profile/avatar'
})

const uploadHeaders = computed(() => {
  return {
    Authorization: `Bearer ${authStore.accessToken}`
  }
})

// 个人资料完整度计算
const profileCompleteness = computed(() => {
  const fields = [
    userInfo.username,
    userInfo.full_name,
    userInfo.email,
    userInfo.phone,
    userInfo.department,
    userInfo.position,
    userInfo.bio,
    userInfo.avatar
  ]
  const filledFields = fields.filter(field => field && field.trim()).length
  return Math.round((filledFields / fields.length) * 100)
})

// 完整度颜色
const getCompletenessColor = (percentage: number) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 用户统计信息
const userStats = computed(() => [
  {
    key: 'register',
    label: '注册时间',
    value: formatDate(userInfo.created_at),
    icon: 'Calendar',
    iconClass: 'stat-icon-blue'
  },
  {
    key: 'login',
    label: '登录次数',
    value: userInfo.login_count || 0,
    icon: 'TrendCharts',
    iconClass: 'stat-icon-green'
  },
  {
    key: 'status',
    label: '账户状态',
    value: userInfo.is_active ? '正常' : '已禁用',
    icon: userInfo.is_active ? 'CircleCheck' : 'CircleClose',
    iconClass: userInfo.is_active ? 'stat-icon-success' : 'stat-icon-danger'
  }
])

// 安全评分计算
const securityScore = computed(() => {
  let score = 0

  // 基础信息完整性 (30分)
  if (userInfo.email) score += 10
  if (userInfo.phone) score += 10
  if (userInfo.full_name) score += 10

  // 双因素认证 (40分)
  if (userInfo.two_factor_enabled) score += 40

  // 密码强度 (20分) - 假设有密码强度检查
  score += 15 // 默认给15分

  // 最近登录活跃度 (10分)
  if (userInfo.last_login) {
    const lastLogin = new Date(userInfo.last_login)
    const now = new Date()
    const daysDiff = (now.getTime() - lastLogin.getTime()) / (1000 * 3600 * 24)
    if (daysDiff <= 7) score += 10
    else if (daysDiff <= 30) score += 5
  }

  return Math.min(score, 100)
})

// 安全评分颜色
const getSecurityScoreColor = (score: number) => {
  if (score >= 80) return '#67c23a'
  if (score >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 安全评分文本
const getSecurityScoreText = (score: number) => {
  if (score >= 80) return '安全'
  if (score >= 60) return '一般'
  return '较低'
}



// 个人资料完善建议
const profileSuggestions = computed(() => {
  const suggestions = []

  if (!userInfo.full_name) {
    suggestions.push({
      field: 'full_name',
      text: '添加真实姓名',
      icon: 'Avatar'
    })
  }

  if (!userInfo.email) {
    suggestions.push({
      field: 'email',
      text: '添加邮箱地址',
      icon: 'Message'
    })
  }

  if (!userInfo.phone) {
    suggestions.push({
      field: 'phone',
      text: '添加手机号码',
      icon: 'Phone'
    })
  }

  if (!userInfo.department) {
    suggestions.push({
      field: 'department',
      text: '添加所属部门',
      icon: 'OfficeBuilding'
    })
  }

  if (!userInfo.position) {
    suggestions.push({
      field: 'position',
      text: '添加职位信息',
      icon: 'Briefcase'
    })
  }

  if (!userInfo.bio) {
    suggestions.push({
      field: 'bio',
      text: '添加个人简介',
      icon: 'Document'
    })
  }

  if (!userInfo.avatar) {
    suggestions.push({
      field: 'avatar',
      text: '上传头像',
      icon: 'Camera'
    })
  }

  return suggestions
})



// 生命周期
onMounted(async () => {
  // 调试信息
  console.log('当前用户信息:', authStore.user)
  console.log('是否为超级管理员:', authStore.user?.is_superuser)
  console.log('用户角色:', authStore.user?.role)

  loading.value = true
  try {
    // 串行加载，避免并发请求导致的token刷新冲突
    await loadUserProfile()
    await loadLoginHistory()

    // 如果是超级管理员，加载管理员用户列表
    if (authStore.user?.is_superuser) {
      await loadAdminUsers()
    }
  } catch (error) {
    console.error('Profile页面初始化失败:', error)
  } finally {
    loading.value = false

    // 页面加载完成后的欢迎提示
    setTimeout(() => {
      if (userInfo.username) {
        ElMessage({
          message: `欢迎回来，${userInfo.username}！`,
          type: 'success',
          duration: 3000,
          showClose: true
        })
      }
    }, 500)
  }
})

// 方法
const loadUserProfile = async () => {
  try {
    const data = await profileApi.getProfile()

    Object.assign(userInfo, data)
    Object.assign(basicForm, {
      username: data.username,
      full_name: data.full_name,
      email: data.email,
      phone: data.phone,
      department: data.department,
      position: data.position,
      bio: data.bio
    })

  } catch (error: any) {
    // 如果是认证错误，不显示错误信息（由拦截器处理）
    if (error?.response?.status !== 401) {
      console.warn('无法加载用户资料，可能未登录或权限不足')
    }
    throw error // 重新抛出错误，让调用者处理
  }
}

const saveBasicInfo = async () => {
  if (!basicFormRef.value) return

  try {
    await basicFormRef.value.validate()
    saving.value = true

    // 显示保存中的提示
    const loadingMessage = ElMessage({
      message: '正在保存基本信息...',
      type: 'info',
      duration: 0,
      showClose: false
    })

    await profileApi.updateProfile(basicForm)
    Object.assign(userInfo, basicForm)

    loadingMessage.close()
    ElMessage.success('基本信息保存成功')
  } catch (error: any) {
    if (error.response?.data?.detail) {
      ElMessage.error(error.response.data.detail)
    } else if (error.response?.data?.errors) {
      // 处理字段验证错误
      const errors = error.response.data.errors
      const errorMessages = Object.values(errors).flat().join(', ')
      ElMessage.error(`保存失败：${errorMessages}`)
    } else {
      ElMessage.error('保存基本信息失败，请稍后重试')
    }
  } finally {
    saving.value = false
  }
}

const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    changingPassword.value = true
    
    await profileApi.changePassword({
      current_password: passwordForm.current_password,
      new_password: passwordForm.new_password
    })
    
    resetPasswordForm()
    ElMessage.success('密码修改成功')
  } catch (error) {
    ElMessage.error('密码修改失败')
  } finally {
    changingPassword.value = false
  }
}



const loadLoginHistory = async () => {
  loginHistoryLoading.value = true
  try {
    const data = await profileApi.getLoginHistory({
      page: loginHistoryPage.value,
      page_size: loginHistoryPageSize.value
    })

    loginHistory.value = data.items
    loginHistoryTotal.value = data.total
  } catch (error: any) {
    // 如果是认证错误，不显示错误信息（由拦截器处理）
    if (error?.response?.status !== 401) {
      console.warn('无法加载登录历史，可能未登录或权限不足')
    }
    throw error // 重新抛出错误，让调用者处理
  } finally {
    loginHistoryLoading.value = false
  }
}

const handleAvatarSuccess: UploadProps['onSuccess'] = (response) => {
  userInfo.avatar = response.avatar_url
  ElMessage.success('头像上传成功')
}

const beforeAvatarUpload: UploadProps['beforeUpload'] = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}



const enableTwoFactor = async () => {
  try {
    await profileApi.enableTwoFactor()
    userInfo.two_factor_enabled = true
    ElMessage.success('双因素认证已启用')
  } catch (error) {
    ElMessage.error('启用双因素认证失败')
  }
}

const disableTwoFactor = async () => {
  try {
    await ElMessageBox.confirm('确定要禁用双因素认证吗？', '确认操作', {
      type: 'warning'
    })
    
    await profileApi.disableTwoFactor()
    userInfo.two_factor_enabled = false
    ElMessage.success('双因素认证已禁用')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('禁用双因素认证失败')
    }
  }
}

const resetBasicForm = () => {
  basicFormRef.value?.resetFields()
}

const resetPasswordForm = () => {
  passwordFormRef.value?.resetFields()
  passwordStrength.value = null
}

const getRoleText = (role: string) => {
  const roleMap: Record<string, string> = {
    superadmin: '超级管理员',
    admin: '管理员',
    editor: '编辑员',
    viewer: '查看员',
    user: '普通用户',
    moderator: '版主'
  }
  return roleMap[role] || role
}

const formatTime = (time: string) => {
  return time ? new Date(time).toLocaleString('zh-CN') : '从未'
}

const formatDate = (date: string) => {
  return date ? new Date(date).toLocaleDateString('zh-CN') : '未知'
}

// 现代化功能方法
const handleTabChange = (tabName: string) => {
  console.log('切换到标签页:', tabName)
}

const showSecurityDetails = () => {
  // 计算各项安全指标
  const securityItems = [
    {
      category: '基础信息完整性',
      score: (userInfo.email ? 10 : 0) + (userInfo.phone ? 10 : 0) + (userInfo.full_name ? 10 : 0),
      maxScore: 30,
      items: [
        { name: '邮箱地址', status: userInfo.email, icon: userInfo.email ? 'success' : 'danger' },
        { name: '手机号码', status: userInfo.phone, icon: userInfo.phone ? 'success' : 'danger' },
        { name: '真实姓名', status: userInfo.full_name, icon: userInfo.full_name ? 'success' : 'danger' }
      ]
    },
    {
      category: '账户安全',
      score: (userInfo.two_factor_enabled ? 40 : 0) + 15, // 密码强度默认15分
      maxScore: 55,
      items: [
        { name: '双因素认证', status: userInfo.two_factor_enabled, icon: userInfo.two_factor_enabled ? 'success' : 'warning' },
        { name: '密码强度', status: '良好', icon: 'success' },
        { name: '密码更新', status: '建议定期更换', icon: 'warning' }
      ]
    },
    {
      category: '登录安全',
      score: userInfo.last_login ? 15 : 0,
      maxScore: 15,
      items: [
        { name: '最近登录', status: userInfo.last_login ? formatTime(userInfo.last_login) : '无记录', icon: userInfo.last_login ? 'success' : 'danger' },
        { name: '登录次数', status: `${userInfo.login_count || 0} 次`, icon: 'info' },
        { name: '异常登录', status: '无异常', icon: 'success' }
      ]
    }
  ]

  const getIconClass = (icon: string) => {
    const iconMap = {
      success: 'el-icon-circle-check',
      warning: 'el-icon-warning',
      danger: 'el-icon-circle-close',
      info: 'el-icon-info'
    }
    return iconMap[icon] || 'el-icon-info'
  }

  const getIconColor = (icon: string) => {
    const colorMap = {
      success: '#67c23a',
      warning: '#e6a23c',
      danger: '#f56c6c',
      info: '#409eff'
    }
    return colorMap[icon] || '#409eff'
  }

  const totalScore = securityItems.reduce((sum, item) => sum + item.score, 0)
  const maxTotalScore = securityItems.reduce((sum, item) => sum + item.maxScore, 0)
  const scorePercentage = Math.round((totalScore / maxTotalScore) * 100)

  const securityLevel = scorePercentage >= 80 ? '优秀' : scorePercentage >= 60 ? '良好' : scorePercentage >= 40 ? '一般' : '较差'
  const levelColor = scorePercentage >= 80 ? '#67c23a' : scorePercentage >= 60 ? '#409eff' : scorePercentage >= 40 ? '#e6a23c' : '#f56c6c'

  const recommendations = []
  if (!userInfo.email) recommendations.push('完善邮箱地址以接收安全通知')
  if (!userInfo.phone) recommendations.push('绑定手机号码以增强账户安全')
  if (!userInfo.full_name) recommendations.push('填写真实姓名以完善身份信息')
  if (!userInfo.two_factor_enabled) recommendations.push('启用双因素认证以大幅提升安全性')
  if (!userInfo.last_login) recommendations.push('定期登录以保持账户活跃状态')

  ElMessageBox.alert(
    `
    <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6;">
      <!-- 安全评分概览 -->
      <div style="text-align: center; margin-bottom: 24px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; color: white;">
        <div style="font-size: 18px; font-weight: 600; margin-bottom: 8px;">安全评分</div>
        <div style="font-size: 36px; font-weight: 700; margin-bottom: 4px;">${scorePercentage}</div>
        <div style="font-size: 14px; opacity: 0.9;">安全等级：${securityLevel}</div>
        <div style="margin-top: 12px; background: rgba(255,255,255,0.2); border-radius: 8px; height: 8px; overflow: hidden;">
          <div style="height: 100%; background: white; width: ${scorePercentage}%; transition: width 0.3s ease;"></div>
        </div>
      </div>

      <!-- 详细评分项目 -->
      <div style="margin-bottom: 24px;">
        ${securityItems.map(category => `
          <div style="margin-bottom: 20px; border: 1px solid #e4e7ed; border-radius: 8px; overflow: hidden;">
            <div style="background: #f8f9fa; padding: 12px 16px; border-bottom: 1px solid #e4e7ed; display: flex; justify-content: space-between; align-items: center;">
              <span style="font-weight: 600; color: #303133;">${category.category}</span>
              <span style="font-size: 12px; color: #909399;">${category.score}/${category.maxScore} 分</span>
            </div>
            <div style="padding: 12px 16px;">
              ${category.items.map(item => `
                <div style="display: flex; align-items: center; margin-bottom: 8px; padding: 8px; border-radius: 6px; background: #fafafa;">
                  <i class="${getIconClass(item.icon)}" style="color: ${getIconColor(item.icon)}; margin-right: 8px; font-size: 14px;"></i>
                  <span style="flex: 1; font-size: 14px; color: #606266;">${item.name}</span>
                  <span style="font-size: 13px; color: #909399;">${typeof item.status === 'boolean' ? (item.status ? '已启用' : '未启用') : item.status}</span>
                </div>
              `).join('')}
            </div>
          </div>
        `).join('')}
      </div>

      <!-- 安全建议 -->
      ${recommendations.length > 0 ? `
        <div style="background: #fff7e6; border: 1px solid #ffd591; border-radius: 8px; padding: 16px; margin-bottom: 16px;">
          <div style="display: flex; align-items: center; margin-bottom: 12px;">
            <i class="el-icon-warning" style="color: #fa8c16; margin-right: 8px; font-size: 16px;"></i>
            <span style="font-weight: 600; color: #d46b08;">安全建议</span>
          </div>
          <ul style="margin: 0; padding-left: 20px; color: #8c5700;">
            ${recommendations.map(rec => `<li style="margin-bottom: 4px; font-size: 13px;">${rec}</li>`).join('')}
          </ul>
        </div>
      ` : ''}

      <!-- 安全提示 -->
      <div style="background: #f0f9ff; border: 1px solid #bae7ff; border-radius: 8px; padding: 16px;">
        <div style="display: flex; align-items: center; margin-bottom: 8px;">
          <i class="el-icon-info" style="color: #1890ff; margin-right: 8px; font-size: 16px;"></i>
          <span style="font-weight: 600; color: #0050b3;">安全提示</span>
        </div>
        <div style="font-size: 13px; color: #003a8c; line-height: 1.5;">
          • 定期更换密码，使用强密码组合<br>
          • 启用双因素认证可大幅提升账户安全性<br>
          • 注意保护个人信息，避免在不安全的网络环境下登录<br>
          • 发现异常登录活动请及时联系管理员
        </div>
      </div>
    </div>
    `,
    '账户安全详情',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '我知道了',
      customClass: 'security-details-dialog',
      center: false
    }
  )
}

const exportUserData = async () => {
  try {
    ElMessage.info('正在准备导出数据...')

    // 模拟数据导出
    const userData = {
      基本信息: {
        用户名: userInfo.username,
        真实姓名: userInfo.full_name,
        邮箱: userInfo.email,
        手机: userInfo.phone,
        部门: userInfo.department,
        职位: userInfo.position,
        个人简介: userInfo.bio
      },
      账户信息: {
        注册时间: userInfo.created_at,
        最后登录: userInfo.last_login,
        登录次数: userInfo.login_count,
        账户状态: userInfo.is_active ? '正常' : '已禁用',
        双因素认证: userInfo.two_factor_enabled ? '已启用' : '未启用'
      },
      安全信息: {
        安全评分: securityScore.value,
        资料完整度: profileCompleteness.value + '%'
      }
    }

    const dataStr = JSON.stringify(userData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    const link = document.createElement('a')
    link.href = url
    link.download = `个人数据_${userInfo.username}_${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出数据失败:', error)
    ElMessage.error('导出数据失败')
  }
}

// 增强的密码强度检测
const checkPasswordStrength = (password: string) => {
  if (!password) {
    passwordStrength.value = null
    return
  }

  let score = 0
  const suggestions: string[] = []

  // 长度检查
  if (password.length >= 8) {
    score += 25
  } else {
    suggestions.push('密码长度至少8位')
  }

  // 包含小写字母
  if (/[a-z]/.test(password)) {
    score += 15
  } else {
    suggestions.push('包含小写字母')
  }

  // 包含大写字母
  if (/[A-Z]/.test(password)) {
    score += 15
  } else {
    suggestions.push('包含大写字母')
  }

  // 包含数字
  if (/\d/.test(password)) {
    score += 15
  } else {
    suggestions.push('包含数字')
  }

  // 包含特殊字符
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score += 20
  } else {
    suggestions.push('包含特殊字符')
  }

  // 长度奖励
  if (password.length >= 12) {
    score += 10
  }

  let level = 'weak'
  let text = '弱'

  if (score >= 70) {
    level = 'strong'
    text = '强'
  } else if (score >= 40) {
    level = 'medium'
    text = '中'
  }

  passwordStrength.value = {
    score: Math.min(score, 100),
    level,
    text,
    suggestions
  }
}

// 头像上传相关方法
const openAvatarDialog = () => {
  avatarDialogVisible.value = true
}

const handleAvatarSelect = (file: File) => {
  // 验证文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件！')
    return false
  }

  // 验证文件大小 (5MB)
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB！')
    return false
  }

  // 创建预览URL
  avatarFile.value = file
  avatarPreviewUrl.value = URL.createObjectURL(file)

  return false // 阻止自动上传
}

const resetAvatar = () => {
  if (avatarPreviewUrl.value) {
    URL.revokeObjectURL(avatarPreviewUrl.value)
  }
  avatarFile.value = null
  avatarPreviewUrl.value = ''
}

const uploadAvatar = async () => {
  if (!avatarFile.value) {
    ElMessage.error('请先选择头像文件')
    return
  }

  avatarUploading.value = true
  uploadProgress.value = 0

  try {
    const formData = new FormData()
    formData.append('file', avatarFile.value)

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 20
      }
    }, 200)

    // 这里应该调用实际的头像上传API
    // const response = await profileApi.uploadAvatar(formData)

    // 模拟上传过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    clearInterval(progressInterval)
    uploadProgress.value = 100

    // 更新用户头像
    userInfo.avatar = avatarPreviewUrl.value

    ElMessage.success('头像上传成功')

    // 延迟关闭对话框，让用户看到100%的进度
    setTimeout(() => {
      avatarDialogVisible.value = false
      resetAvatar()
    }, 500)

  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败')
  } finally {
    setTimeout(() => {
      avatarUploading.value = false
      uploadProgress.value = 0
    }, 500)
  }
}



// 个人资料完善相关方法
const focusField = (field: string) => {
  if (field === 'avatar') {
    openAvatarDialog()
    return
  }

  // 切换到基本信息标签页
  activeTab.value = 'basic'

  // 等待DOM更新后聚焦到对应字段
  nextTick(() => {
    const fieldElement = document.querySelector(`[data-field="${field}"]`) as HTMLElement
    if (fieldElement) {
      fieldElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
      const input = fieldElement.querySelector('input, textarea') as HTMLInputElement
      if (input) {
        input.focus()
      }
    }
  })
}



// 管理员管理相关方法
const loadAdminUsers = async () => {
  adminLoading.value = true
  try {
    const response = await adminApi.getAdminUsers({
      page: adminPagination.page,
      page_size: adminPagination.pageSize,
      search: adminSearchKeyword.value || undefined
    })

    adminUsers.value = (response as any).items || []
    adminPagination.total = (response as any).total || 0
  } catch (error: any) {
    // 如果是认证错误，不显示错误信息（由拦截器处理）
    if (error?.response?.status !== 401) {
      console.warn('无法加载管理员用户列表，可能权限不足')
    }
    throw error // 重新抛出错误，让调用者处理
  } finally {
    adminLoading.value = false
  }
}

const searchAdminUsers = () => {
  adminPagination.page = 1
  loadAdminUsers()
}

const openAdminDialog = (mode: 'create' | 'edit', admin?: AdminUser) => {
  adminDialogMode.value = mode
  if (mode === 'edit' && admin) {
    currentEditingAdmin.value = admin
    Object.assign(adminForm, {
      username: admin.username,
      email: admin.email,
      password: '', // 编辑时不显示密码
      full_name: admin.full_name || '',
      role: admin.role,
      is_active: admin.is_active,
      is_superuser: admin.is_superuser,
      phone: admin.phone || '',
      department: admin.department || '',
      position: admin.position || '',
      bio: admin.bio || ''
    })
  } else {
    resetAdminForm()
  }
  adminDialogVisible.value = true
}

const resetAdminForm = () => {
  Object.assign(adminForm, {
    username: '',
    email: '',
    password: '',
    full_name: '',
    role: 'viewer',
    is_active: true,
    is_superuser: false,
    phone: '',
    department: '',
    position: '',
    bio: ''
  })
  currentEditingAdmin.value = null
  adminFormRef.value?.clearValidate()
}

const saveAdmin = async () => {
  if (!adminFormRef.value) return

  try {
    await adminFormRef.value.validate()
    saving.value = true

    if (adminDialogMode.value === 'create') {
      await adminApi.createAdminUser(adminForm)
      ElMessage.success('管理员创建成功')
    } else if (currentEditingAdmin.value) {
      const updateData = { ...adminForm }
      delete updateData.password // 更新时不包含密码
      await adminApi.updateAdminUser(currentEditingAdmin.value.id, updateData)
      ElMessage.success('管理员更新成功')
    }

    adminDialogVisible.value = false
    loadAdminUsers()
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '操作失败')
  } finally {
    saving.value = false
  }
}

const toggleAdminStatus = async (admin: AdminUser) => {
  try {
    await adminApi.toggleAdminUserStatus(admin.id)
    ElMessage.success(`管理员已${admin.is_active ? '禁用' : '激活'}`)
    loadAdminUsers()
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '操作失败')
  }
}

const deleteAdmin = async (admin: AdminUser) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除管理员 "${admin.username}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消'
      }
    )

    await adminApi.deleteAdminUser(admin.id)
    ElMessage.success('管理员删除成功')
    loadAdminUsers()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.response?.data?.detail || '删除失败')
    }
  }
}

const getRoleTagType = (role: string) => {
  const typeMap: Record<string, string> = {
    superadmin: 'danger',
    admin: 'warning',
    editor: 'primary',
    viewer: 'info'
  }
  return typeMap[role] || 'info'
}

const formatDateTime = (dateTime: string) => {
  return dateTime ? new Date(dateTime).toLocaleString('zh-CN') : '从未'
}
</script>

<style lang="scss" scoped>
// 现代化页面样式
.profile-page-modern {
  min-height: 100vh;
  background: #f8fafc;

  .page-header-modern {
    background: white;
    color: #2c3e50;
    padding: 32px 24px;
    margin-bottom: 24px;
    border-radius: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border-bottom: 1px solid #e5e7eb;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-text {
        .page-title {
          display: flex;
          align-items: center;
          gap: 12px;
          margin: 0 0 8px 0;
          font-size: 28px;
          font-weight: 600;
          color: #1f2937;

          .title-icon {
            font-size: 32px;
            color: #6366f1;
          }
        }

        .page-subtitle {
          margin: 0;
          font-size: 16px;
          color: #6b7280;
          font-weight: 400;
        }
      }

      .header-actions {
        .el-button {
          border-radius: 8px;
          padding: 12px 24px;
          font-weight: 500;
          background: #6366f1;
          border: 1px solid #6366f1;
          color: white;

          &:hover {
            background: #5855eb;
            border-color: #5855eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
          }
        }
      }
    }
  }

  .profile-container-modern {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;

    @media (max-width: 1024px) {
      gap: 16px;
    }
  }

  // 现代化侧边栏
  .profile-sidebar-modern {
    .user-profile-card {
      position: relative;
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid #e5e7eb;
      transition: all 0.3s ease;
      margin-bottom: 24px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
      }

      // 横排布局容器
      .profile-sections-horizontal {
        display: flex;
        flex-wrap: wrap;
        gap: 0;
        
        @media (max-width: 1200px) {
          flex-direction: column;
        }
      }

      .card-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 120px;
        background: transparent;
      }

      .avatar-section-modern {
        position: relative;
        padding: 20px;
        text-align: center;
        flex: 1;
        min-width: 280px;
        border-right: 1px solid #f0f0f0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        
        @media (max-width: 1200px) {
          border-right: none;
          border-bottom: 1px solid #f0f0f0;
          min-width: auto;
        }

        .avatar-container {
          position: relative;
          display: inline-block;
          margin-bottom: 16px;

          .avatar-uploader-modern {
              .avatar-wrapper {
                position: relative;
                width: 80px;
                height: 80px;
                border-radius: 50%;
                overflow: hidden;
                border: 3px solid white;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                cursor: pointer;
                transition: all 0.3s ease;

              &:hover {
                transform: scale(1.02);
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);

                .avatar-overlay {
                  opacity: 1;
                }
              }

              .avatar-modern {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }

              .avatar-placeholder {
                width: 100%;
                height: 100%;
                background: #6366f1;
                display: flex;
                align-items: center;
                justify-content: center;

                .avatar-icon {
                  font-size: 40px;
                  color: white;
                }
              }

              .avatar-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.6);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: all 0.3s ease;

                .upload-icon {
                  font-size: 24px;
                  color: white;
                  margin-bottom: 4px;
                }

                .upload-text {
                  font-size: 12px;
                  color: white;
                  font-weight: 500;
                }
              }
            }
          }

          .online-indicator {
            position: absolute;
            bottom: 8px;
            right: 8px;
            width: 16px;
            height: 16px;
            background: #67c23a;
            border: 3px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          }
        }

        .user-info-modern {
          .username {
            margin: 0 0 8px 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
          }

          .user-meta {
            margin-bottom: 12px;

            .role-tag-modern {
              border-radius: 20px;
              padding: 6px 16px;
              font-weight: 500;

              .role-icon {
                margin-right: 4px;
              }
            }
          }

          .last-login-modern {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            margin: 0;
            font-size: 14px;
            color: #909399;

            .time-icon {
              font-size: 16px;
            }
          }
        }
      }

      // 个人资料完整度样式
      .profile-completeness {
        padding: 20px;
        border-right: 1px solid #f0f0f0;
        flex: 1;
        min-width: 250px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        
        @media (max-width: 1200px) {
          border-right: none;
          border-bottom: 1px solid #f0f0f0;
          min-width: auto;
        }

        .completeness-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .completeness-title {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
          }

          .completeness-percentage {
            font-size: 16px;
            font-weight: 600;
            color: #409eff;
          }
        }

        .completeness-progress {
          margin-bottom: 8px;

          :deep(.el-progress-bar__outer) {
            border-radius: 10px;
            background: #f0f0f0;
          }

          :deep(.el-progress-bar__inner) {
            border-radius: 10px;
            transition: all 0.3s ease;
          }
        }

        .completeness-suggestions {
          margin-top: 12px;

          .suggestions-header {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 8px;

            .suggestion-icon {
              font-size: 14px;
              color: #409eff;
            }

            .suggestion-title {
              font-size: 12px;
              font-weight: 500;
              color: #303133;
            }
          }

          .suggestions-list {
            margin: 0;
            padding: 0;
            list-style: none;

            .suggestion-item {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 6px 0;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(64, 158, 255, 0.05);
                border-radius: 6px;
                padding: 6px 8px;
              }

              .item-icon {
                font-size: 14px;
                color: #909399;
                flex-shrink: 0;
              }

              .item-text {
                flex: 1;
                font-size: 12px;
                color: #606266;
              }

              .item-action {
                font-size: 11px;
                padding: 2px 8px;
                border-radius: 10px;
                color: #409eff;

                &:hover {
                  background: #f0f4ff;
                  color: #66b1ff;
                }
              }
            }
          }
        }
      }

      // 统计信息卡片样式
      .stats-section-modern {
        padding: 20px;
        border-right: 1px solid #f0f0f0;
        flex: 1;
        min-width: 220px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        
        @media (max-width: 1200px) {
          border-right: none;
          border-bottom: 1px solid #f0f0f0;
          min-width: auto;
        }

        .stat-card {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 0;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(64, 158, 255, 0.05);
            border-radius: 8px;
            padding: 12px 8px;
          }

          .stat-icon {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;

            &.stat-icon-blue {
              background: #6366f1;
              color: white;
            }

            &.stat-icon-green {
              background: #10b981;
              color: white;
            }

            &.stat-icon-success {
              background: #10b981;
              color: white;
            }

            &.stat-icon-danger {
              background: #ef4444;
              color: white;
            }
          }

          .stat-content {
            flex: 1;

            .stat-label {
              display: block;
              font-size: 12px;
              color: #909399;
              margin-bottom: 2px;
            }

            .stat-value {
              display: block;
              font-size: 14px;
              font-weight: 500;
              color: #303133;
            }
          }
        }
      }

      // 安全评分卡片样式
      .security-score-card {
        padding: 20px;
        background: white;
        flex: 1;
        min-width: 280px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        
        @media (max-width: 1200px) {
          min-width: auto;
        }

        .security-content {
          width: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;

          .score-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;

            .security-icon {
              font-size: 18px;
              color: #409eff;
            }

            .score-title {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
            }
          }

          .score-display {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;

            .score-circle {
              :deep(.el-progress-circle) {
                .el-progress__text {
                  font-size: 14px !important;
                  font-weight: 600;
                }
              }
            }

            .score-info {
              flex: 1;

              .score-text {
                display: block;
                font-size: 16px;
                font-weight: 500;
                color: #303133;
                margin-bottom: 4px;
              }

              .score-detail-btn {
                font-size: 12px;
                padding: 0;
                color: #409eff;

                &:hover {
                  color: #66b1ff;
                }
              }
            }
          }

          .security-summary-content {
            width: 100%;
            border-top: 1px solid #e4e7ed;
            padding-top: 16px;

            .summary-header {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 12px;
              justify-content: center;

              .summary-icon {
                font-size: 16px;
                color: #409eff;
              }

              .summary-title {
                font-size: 13px;
                font-weight: 500;
                color: #303133;
              }
            }

            .security-items {
              display: flex;
              flex-direction: column;
              gap: 6px;

              .security-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 6px 8px;
                border-radius: 4px;
                transition: all 0.3s ease;

                &:hover {
                  background: rgba(64, 158, 255, 0.05);
                }

                .item-icon {
                  font-size: 12px;
                  flex-shrink: 0;

                  &.success {
                    color: #67c23a;
                  }

                  &.warning {
                    color: #e6a23c;
                  }

                  &.danger {
                    color: #f56c6c;
                  }
                }

                .item-text {
                  font-size: 11px;
                  color: #606266;
                  flex: 1;
                }
              }
            }
          }
        }
      }


    }
  }

  // 现代化主要内容区域
  .profile-content-modern {
    width: 100%;
    
    .profile-tabs-modern {
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid #e5e7eb;
      overflow: hidden;

      :deep(.el-tabs__header) {
        margin: 0;
        background: #f8fafc;
        border-bottom: 1px solid #e4e7ed;

        .el-tabs__nav-wrap {
          padding: 0 24px;

          .el-tabs__nav {
            border: none;

            .el-tabs__item {
              border: none;
              padding: 16px 0;
              margin-right: 32px;
              font-weight: 500;
              transition: all 0.3s ease;

              .tab-label {
                display: flex;
                align-items: center;
                gap: 8px;

                .el-icon {
                  font-size: 16px;
                }
              }

              &.is-active {
                color: #409eff;

                .tab-label .el-icon {
                  transform: scale(1.1);
                }
              }

              &:hover {
                color: #66b1ff;
              }
            }

            .el-tabs__active-bar {
              height: 3px;
              border-radius: 2px;
              background: #6366f1;
            }
          }
        }
      }

      :deep(.el-tabs__content) {
        padding: 0;

        .el-tab-pane {
          .tab-content-wrapper {
            padding: 24px;

            .content-header {
              margin-bottom: 24px;

              .content-title {
                margin: 0 0 8px 0;
                font-size: 20px;
                font-weight: 600;
                color: #303133;
              }

              .content-subtitle {
                margin: 0;
                font-size: 14px;
                color: #909399;
              }
            }
          }
          
          // 管理员管理模块特殊处理，移除内边距让表格占满宽度
          &#pane-admin .tab-content-wrapper {
            padding: 0 !important;
          }
        }
      }
    }
  }
}

  // 现代化表单样式
  .form-card-modern {
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 24px;
    overflow: hidden;

    :deep(.el-card__header) {
      background: white;
      border-bottom: 1px solid #e4e7ed;
      padding: 20px 24px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;

          .header-icon {
            font-size: 20px;
            color: #409eff;
          }

          .header-text {
            .header-title {
              margin: 0 0 4px 0;
              font-size: 16px;
              font-weight: 600;
              color: #303133;
            }

            .header-subtitle {
              margin: 0;
              font-size: 12px;
              color: #909399;
            }
          }
        }

        .header-right {
          .el-tag {
            border-radius: 12px;
            border: none;
            font-size: 11px;
            padding: 4px 12px;

            .el-icon {
              margin-right: 4px;
            }
          }
        }
      }
    }

    :deep(.el-card__body) {
      padding: 24px;
    }
  }

  .profile-form-modern {
    .form-section {
      margin-bottom: 24px;

      .el-form-item {
        margin-bottom: 0;

        :deep(.el-form-item__label) {
          font-weight: 500;
          color: #303133;
          line-height: 1.5;
        }

        :deep(.el-form-item__content) {
          .input-with-icon {
            position: relative;

            .input-icon {
              position: absolute;
              left: 12px;
              top: 50%;
              transform: translateY(-50%);
              font-size: 16px;
              color: #c0c4cc;
              z-index: 1;
            }

            .modern-input {
              :deep(.el-input__wrapper) {
                border-radius: 12px;
                padding-left: 40px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                border: 1px solid #e4e7ed;
                transition: all 0.3s ease;

                &:hover {
                  border-color: #c0c4cc;
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
                }

                &.is-focus {
                  border-color: #409eff;
                  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
                }

                .el-input__inner {
                  padding-left: 0;
                }
              }
            }
          }

          .textarea-with-icon {
            position: relative;

            .textarea-icon {
              position: absolute;
              left: 12px;
              top: 12px;
              font-size: 16px;
              color: #c0c4cc;
              z-index: 1;
            }

            .modern-textarea {
              :deep(.el-textarea__inner) {
                border-radius: 12px;
                padding-left: 40px;
                padding-top: 12px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                border: 1px solid #e4e7ed;
                transition: all 0.3s ease;
                resize: vertical;

                &:hover {
                  border-color: #c0c4cc;
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
                }

                &:focus {
                  border-color: #409eff;
                  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
                }
              }
            }
          }
        }
      }

      .form-tip-modern {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-top: 8px;
        font-size: 12px;
        color: #909399;

        .tip-icon {
          font-size: 14px;
          color: #409eff;
        }
      }
    }

    .form-actions {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      margin-top: 32px;
      padding-top: 24px;
      border-top: 1px solid #f0f0f0;

      .action-btn {
        border-radius: 20px;
        padding: 12px 24px;
        font-weight: 500;
        transition: all 0.3s ease;

        &.primary-btn {
          background: #409eff;
          border: none;

          &:hover {
            background: #66b1ff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
          }
        }

        &.secondary-btn {
          background: #f8f9fa;
          border: 1px solid #e4e7ed;
          color: #606266;

          &:hover {
            background: #e9ecef;
            border-color: #c0c4cc;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }

        .el-icon {
          margin-right: 6px;
        }
      }
    }
  }

  // 密码强度指示器现代化样式
  .password-strength-modern {
    margin-top: 12px;
    padding: 12px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e4e7ed;

    .strength-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .strength-label {
        font-size: 12px;
        color: #606266;
        font-weight: 500;
      }

      .strength-score {
        font-size: 12px;
        font-weight: 600;
      }
    }

    .strength-bar-modern {
      width: 100%;
      height: 6px;
      background: #f0f0f0;
      border-radius: 3px;
      overflow: hidden;
      margin-bottom: 8px;

      .strength-fill-modern {
        height: 100%;
        transition: all 0.3s ease;
        border-radius: 3px;

        &.weak {
          background: #f56c6c;
        }

        &.medium {
          background: #e6a23c;
        }

        &.strong {
          background: #67c23a;
        }
      }
    }

    .strength-tips {
      display: flex;
      align-items: flex-start;
      gap: 6px;

      .tips-icon {
        font-size: 14px;
        color: #409eff;
        margin-top: 2px;
        flex-shrink: 0;
      }

      .tips-list {
        margin: 0;
        padding: 0;
        list-style: none;
        font-size: 11px;
        color: #909399;

        li {
          margin-bottom: 2px;

          &:before {
            content: "• ";
            color: #409eff;
            margin-right: 4px;
          }
        }
      }
    }
  }

  // 骨架屏样式
  .user-skeleton {
    padding: 24px;
    text-align: center;

    .skeleton-avatar-container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  .fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .completeness-skeleton,
  .stats-skeleton,
  .security-skeleton {
    padding: 20px 24px;
  }

  .stats-skeleton {
    .stat-skeleton-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
    }
  }

  .login-history-skeleton {
    .table-skeleton {
      .table-header-skeleton {
        display: flex;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 8px;
      }

      .table-row-skeleton {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f8f9fa;
      }
    }
  }

  // 现代化表格样式
  .modern-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    :deep(.el-table__header) {
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);

      th {
        background: transparent !important;
        border-bottom: 1px solid #e4e7ed;
        font-weight: 600;
        color: #303133;
      }
    }

    :deep(.el-table__body) {
      tr {
        transition: all 0.3s ease;

        &:hover {
          background: rgba(64, 158, 255, 0.05) !important;
        }

        td {
          border-bottom: 1px solid #f8f9fa;

          .time-cell,
          .ip-cell,
          .device-cell,
          .location-cell {
            display: flex;
            align-items: center;
            gap: 8px;

            .el-icon {
              font-size: 14px;
              color: #909399;
            }
          }

          .device-cell {
            .device-text {
              max-width: 200px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .status-tag {
            border-radius: 12px;

            .status-icon {
              margin-right: 4px;
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .table-pagination-modern {
    margin-top: 20px;
    display: flex;
    justify-content: center;

    .modern-pagination {
      :deep(.el-pagination) {
        .el-pager li {
          border-radius: 6px;
          margin: 0 2px;
          transition: all 0.3s ease;

          &:hover {
            background: #f0f4ff;
          }

          &.is-active {
            background: #409eff;
            color: white;
          }
        }

        .btn-prev,
        .btn-next {
          border-radius: 6px;
          transition: all 0.3s ease;

          &:hover {
            background: #f0f4ff;
          }
        }
      }
    }
  }

  // 头像上传对话框样式
  .avatar-upload-dialog {
    .upload-area {
      margin-bottom: 20px;

      .avatar-upload-input {
        width: 100%;

        :deep(.el-upload) {
          width: 100%;

          .el-upload-dragger {
            width: 100%;
            height: 200px;
            border-radius: 12px;
            border: 2px dashed #d9d9d9;
            background: #fafafa;
            transition: all 0.3s ease;

            &:hover {
              border-color: #409eff;
              background: #f0f4ff;
            }

            .upload-placeholder {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 100%;

              .upload-icon-large {
                font-size: 48px;
                color: #c0c4cc;
                margin-bottom: 12px;
              }

              .upload-text-large {
                font-size: 16px;
                color: #606266;
                margin-bottom: 8px;
              }

              .upload-hint {
                font-size: 12px;
                color: #909399;
              }
            }

            .avatar-preview-container {
              position: relative;
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;

              .avatar-preview {
                max-width: 100%;
                max-height: 100%;
                border-radius: 8px;
                object-fit: cover;
              }

              .preview-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: all 0.3s ease;
                border-radius: 8px;

                .change-icon {
                  font-size: 24px;
                  color: white;
                  margin-bottom: 8px;
                }

                span {
                  color: white;
                  font-size: 14px;
                }
              }

              &:hover .preview-overlay {
                opacity: 1;
              }
            }
          }
        }
      }
    }

    .avatar-actions {
      .upload-progress {
        text-align: center;
        padding: 20px;

        .el-progress {
          margin-bottom: 12px;

          :deep(.el-progress-bar__outer) {
            border-radius: 10px;
            background: #f0f0f0;
          }

          :deep(.el-progress-bar__inner) {
            border-radius: 10px;
            background: #409eff;
          }
        }

        .progress-text {
          margin: 0;
          font-size: 14px;
          color: #606266;
        }
      }

      .action-buttons {
        display: flex;
        justify-content: center;
        gap: 12px;

        .el-button {
          border-radius: 20px;
          padding: 10px 20px;

          &.el-button--primary {
            background: #409eff;
            border: none;

            &:hover {
              background: #66b1ff;
            }
          }
        }
      }
    }
  }

  // 偏好设置现代化样式
  .preferences-form-modern {
    .select-with-icon {
      position: relative;

      .select-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 16px;
        color: #c0c4cc;
        z-index: 1;
      }

      .modern-select {
        :deep(.el-input__wrapper) {
          padding-left: 40px;
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
          }
        }

        .option-content {
          display: flex;
          flex-direction: column;

          .option-text {
            font-weight: 500;
          }

          .option-desc {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    .theme-selector {
      .theme-radio-group {
        display: flex;
        gap: 16px;

        .theme-radio {
          flex: 1;
          margin: 0;

          :deep(.el-radio__input) {
            display: none;
          }

          :deep(.el-radio__label) {
            padding: 0;
          }

          .theme-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px;
            border: 2px solid #e4e7ed;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              border-color: #c0c4cc;
              background: #f8f9fa;
            }

            .theme-icon {
              font-size: 24px;
              color: #909399;
              margin-bottom: 8px;
            }

            .theme-text {
              font-size: 14px;
              color: #606266;
              font-weight: 500;
            }
          }

          &.is-checked .theme-option {
            border-color: #409eff;
            background: #f0f4ff;

            .theme-icon {
              color: #409eff;
            }

            .theme-text {
              color: #409eff;
            }
          }
        }
      }
    }

    .number-input-with-icon {
      display: flex;
      align-items: center;
      gap: 12px;

      .input-icon {
        font-size: 16px;
        color: #c0c4cc;
      }

      .modern-number-input {
        :deep(.el-input__wrapper) {
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
          }
        }
      }

      .input-suffix {
        font-size: 14px;
        color: #909399;
      }
    }
  }

  // 通知设置样式
  .notification-settings {
    .notification-category {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      .category-header {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 16px;

        .category-icon {
          font-size: 20px;
          color: #409eff;
        }

        .category-info {
          flex: 1;

          .category-title {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }

          .category-desc {
            margin: 0;
            font-size: 12px;
            color: #909399;
          }
        }

        .el-switch {
          :deep(.el-switch__core) {
            border-radius: 20px;
          }
        }
      }

      .notification-options {
        .notification-option {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            background: #f8f9fa;
          }

          .option-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;

            .option-icon {
              font-size: 16px;
              color: #909399;
            }

            .option-text {
              .option-title {
                display: block;
                font-size: 14px;
                font-weight: 500;
                color: #303133;
                margin-bottom: 2px;
              }

              .option-desc {
                display: block;
                font-size: 12px;
                color: #909399;
              }
            }
          }

          .option-checkbox {
            :deep(.el-checkbox__inner) {
              border-radius: 4px;
            }
          }
        }
      }

      .time-settings {
        .time-range {
          margin-bottom: 16px;

          .time-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 8px;
          }

          .time-inputs {
            display: flex;
            align-items: center;
            gap: 12px;

            .time-picker {
              :deep(.el-input__wrapper) {
                border-radius: 8px;
              }
            }

            .time-separator {
              font-size: 14px;
              color: #909399;
            }
          }
        }

        .quiet-mode {
          .quiet-mode-text {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
          }

          .quiet-mode-desc {
            margin: 4px 0 0 24px;
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    .notification-master-switch {
      :deep(.el-switch__core) {
        border-radius: 20px;

        &::after {
          border-radius: 50%;
        }
      }
    }
  }
}

.profile-page {
  .page-header {
    margin-bottom: 20px;
    
    h1 {
      margin: 0 0 4px 0;
      font-size: 24px;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #909399;
    }
  }
  
  .profile-container {
    display: flex;
    gap: 20px;
    
    .profile-sidebar {
      width: 300px;
      flex-shrink: 0;
      
      .profile-card {
        .avatar-section {
          text-align: center;
          padding-bottom: 20px;
          border-bottom: 1px solid var(--el-border-color-lighter);
          
          .avatar-uploader {
            .avatar {
              width: 100px;
              height: 100px;
              border-radius: 50%;
              object-fit: cover;
            }
            
            .avatar-uploader-icon {
              font-size: 28px;
              color: #8c939d;
              width: 100px;
              height: 100px;
              line-height: 100px;
              text-align: center;
              border: 1px dashed var(--el-border-color);
              border-radius: 50%;
            }
          }
          
          .avatar-info {
            margin-top: 16px;
            
            h3 {
              margin: 0 0 8px 0;
              font-size: 18px;
              color: #303133;
            }
            
            .role-tag {
              color: var(--el-color-primary);
              font-size: 14px;
              margin: 0 0 4px 0;
            }
            
            .last-login {
              color: #909399;
              font-size: 12px;
              margin: 0;
            }
          }
        }
        
        .stats-section {
          padding-top: 20px;
          
          .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            
            .stat-label {
              color: #606266;
              font-size: 14px;
            }
            
            .stat-value {
              color: #303133;
              font-weight: 500;
            }
          }
        }
      }
    }
    
    .profile-content {
      flex: 1;
      
      .profile-form {
        max-width: 600px;
        
        .form-tip {
          font-size: 12px;
          color: #909399;
          margin-top: 4px;
        }
        
        .password-strength {
          margin-top: 8px;
          
          .strength-bar {
            width: 100%;
            height: 6px;
            background: var(--el-border-color-lighter);
            border-radius: 3px;
            overflow: hidden;
            
            .strength-fill {
              height: 100%;
              transition: all 0.3s ease;
              
              &.weak {
                background: var(--el-color-danger);
              }
              
              &.medium {
                background: var(--el-color-warning);
              }
              
              &.strong {
                background: var(--el-color-success);
              }
            }
          }
          
          .strength-text {
            margin-top: 4px;
            font-size: 12px;
            color: var(--el-text-color-regular);
          }
        }
      }
      
      .security-section {
        margin-bottom: 32px;
        
        h4 {
          margin: 0 0 16px 0;
          font-size: 16px;
          color: #303133;
        }
        
        .two-factor-auth {
          .auth-status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
          }
          
          .auth-actions {
            margin-bottom: 8px;
          }
          
          .auth-description {
            font-size: 12px;
            color: #909399;
          }
        }
      }
      
      .table-pagination {
        margin-top: 16px;
        text-align: right;
      }
    }
  }
}

// 管理员管理现代化样式
.admin-management-modern {
  padding: 0;
  
  // 页面头部样式
  .admin-header-modern {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    color: white;
    
    .header-info {
      flex: 1;
      
      .page-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: white;
        
        .title-icon {
          font-size: 28px;
        }
      }
      
      .page-subtitle {
        margin: 0;
        font-size: 14px;
        opacity: 0.9;
        color: rgba(255, 255, 255, 0.8);
      }
    }
    
    .header-stats {
      display: flex;
      gap: 16px;
      
      .stat-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 16px 20px;
        text-align: center;
        min-width: 80px;
        
        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: white;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.8);
          margin: 0;
        }
      }
    }
  }
  
  // 工具栏样式
  .admin-toolbar-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: var(--bg-primary);
    border-radius: 12px;
    border: 1px solid var(--border-light);
    
    .toolbar-left {
      flex: 1;
      
      .search-container {
        display: flex;
        gap: 12px;
        max-width: 400px;
        
        .search-input {
          flex: 1;
          
          :deep(.el-input__wrapper) {
            border-radius: 8px;
          }
        }
        
        .search-btn {
          border-radius: 8px;
          padding: 8px 16px;
        }
      }
    }
    
    .toolbar-right {
      .add-btn {
        border-radius: 8px;
        padding: 8px 20px;
        font-weight: 500;
        
        .el-icon {
          margin-right: 6px;
        }
      }
    }
  }
  
  // 表格卡片样式
  .admin-table-card {
    width: 100%;
    border-radius: 12px;
    border: 1px solid var(--border-light);
    overflow: hidden;
    
    :deep(.el-card__body) {
      padding: 0;
    }
    
    // 表格样式
    .admin-table-modern {
      width: 100%;
      
      :deep(.el-table__header) {
        background-color: var(--bg-tertiary);
        
        th {
          background-color: var(--bg-tertiary) !important;
          color: var(--text-primary);
          font-weight: 600;
          font-size: 13px;
          padding: 16px 12px;
          border-bottom: 1px solid var(--border-light);
        }
      }
      
      :deep(.el-table__body) {
        tr {
          transition: all 0.2s ease;
          
          &:hover {
            background-color: var(--bg-accent) !important;
          }
          
          td {
            padding: 16px 12px;
            border-bottom: 1px solid var(--border-lighter);
          }
        }
      }
      
      // 用户单元格样式
      .user-cell {
        display: flex;
        align-items: center;
        gap: 10px;
        
        .user-avatar {
          flex-shrink: 0;
        }
        
        .username {
          font-weight: 500;
          color: var(--text-primary);
        }
      }
      
      // 标签样式
      .role-tag, .status-tag {
        font-size: 12px;
        font-weight: 500;
        
        .tag-icon {
          margin-right: 4px;
          font-size: 12px;
        }
      }
      
      // 登录时间样式
      .login-time {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 13px;
        color: var(--text-secondary);
        
        .time-icon {
          font-size: 14px;
          color: var(--text-tertiary);
        }
      }
      
      // 操作按钮样式
      .action-buttons {
        display: flex;
        gap: 6px;
        
        .action-btn {
          border-radius: 6px;
          font-size: 12px;
          padding: 6px 12px;
          
          .el-icon {
            margin-right: 4px;
            font-size: 12px;
          }
        }
      }
    }
    
    // 表格底部样式
    .table-footer {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background-color: var(--bg-tertiary);
      border-top: 1px solid var(--border-light);
      
      .pagination-info {
        .info-text {
          font-size: 13px;
          color: var(--text-secondary);
        }
      }
      
      .table-pagination {
        :deep(.el-pagination) {
          .el-pager li {
            border-radius: 6px;
            margin: 0 2px;
          }
          
          .btn-prev, .btn-next {
            border-radius: 6px;
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .admin-header-modern {
      flex-direction: column;
      gap: 16px;
      margin: 24px 24px 24px 24px !important;

      .header-stats {
        width: 100%;
        justify-content: center;
      }
    }

    .admin-toolbar-modern {
      flex-direction: column;
      gap: 12px;
      margin: 0 24px 20px 24px !important;

      .toolbar-left {
        width: 100%;

        .search-container {
          max-width: none;
        }
      }
    }

    .admin-table-card {
      margin: 0 !important;
      border-radius: 0 !important;
      border-left: none !important;
      border-right: none !important;
      border-bottom: none !important;

      .admin-table-modern {
        .el-table__cell {
          padding: 8px 6px !important;
        }

        .user-cell {
          gap: 8px;

          .username {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .login-time {
          font-size: 12px;
          gap: 4px;

          .time-icon {
            font-size: 12px;
          }
        }

        .action-buttons {
          gap: 4px;
          justify-content: center;

          .action-btn {
            padding: 4px 8px !important;
            min-width: auto !important;
          }
        }

        .role-tag,
        .status-tag {
          font-size: 11px !important;
          padding: 2px 6px !important;

          .tag-icon {
            font-size: 10px;
          }
        }
      }
    }

    .table-footer {
      flex-direction: column;
      gap: 12px;
      padding: 16px 24px !important;

      .pagination-info {
        text-align: center;
      }
    }
  }
}

// 安全详情弹窗样式
:deep(.security-details-dialog) {
  .el-message-box {
    width: 600px;
    max-width: 90vw;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  .el-message-box__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-message-box__title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }

  .el-message-box__content {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .el-message-box__btns {
    padding: 15px 20px 20px;
    border-top: 1px solid #e4e7ed;
  }

  .el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 10px 24px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
  }
}

// 双因素认证和登录历史现代化样式
.security-card-modern {
  margin-top: 24px;
}

.two-factor-content {
  padding: 8px 0;
}

.auth-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.info-icon {
  font-size: 20px;
  color: #6366f1;
  padding: 8px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 8px;
}

.info-text {
  flex: 1;
}

.info-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.info-desc {
  font-size: 13px;
  color: #6b7280;
}

.auth-actions-modern {
  display: flex;
  justify-content: center;
}

.action-button-modern {
  padding: 12px 24px;
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-button-modern:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.login-history-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 24px;
  padding: 8px;
  border-radius: 8px;
  background: rgba(var(--el-color-primary-rgb), 0.1);
}

.stat-icon.success {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.stat-icon.danger {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.stat-icon.info {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.stat-text {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.login-history-table-modern {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.login-history-table-modern :deep(.el-table__header) {
  background: #f8fafc;
}

.login-history-table-modern :deep(.el-table__header th) {
  background: #f8fafc !important;
  color: #374151;
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid #e5e7eb;
}

.login-history-table-modern :deep(.el-table__row) {
  transition: all 0.2s ease;
}

.login-history-table-modern :deep(.el-table__row:hover) {
  background: #f9fafb;
}

.time-cell-modern,
.ip-cell-modern,
.device-cell-modern,
.location-cell-modern {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 4px 0;
}

.cell-icon {
  font-size: 16px;
  color: #6b7280;
  flex-shrink: 0;
}

.cell-content {
  flex: 1;
  min-width: 0;
}

.cell-main {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-tag-modern {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-tag-modern .tag-icon {
  font-size: 14px;
}

// 登录历史骨架屏样式
.login-history-skeleton-modern {
  padding: 20px;
}

.table-skeleton-modern {
  width: 100%;
}

.table-header-skeleton-modern {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.table-row-skeleton-modern {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.table-row-skeleton-modern:last-child {
  border-bottom: none;
}



  .auth-info {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .login-history-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  :deep(.security-details-dialog) {
    .el-message-box {
      width: 95vw;
      margin: 10px;
    }

    .el-message-box__content {
      padding: 15px;
      max-height: 60vh;
    }
  }
}
</style>
